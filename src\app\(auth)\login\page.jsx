'use client';

import { LoginForm } from '@/components/login-form';
import QrLogin from '@/components/qr-login';
import { Card, CardContent } from '@/components/ui/card';
import { DotBackground } from '@/components/ui/dot-background';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from '@/components/ui/tabs';
import { useAppSelector } from '@/lib/hooks';
import { userRoles } from '@/lib/utils';
import { Mail, QrCode, Smartphone } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { toast } from 'sonner';

export default function LoginPage() {
	const router = useRouter();
	const { authenticatedUser, isLoggedOut } = useAppSelector(
		(store) => store.auth
	);

	useEffect(() => {
		if (isLoggedOut === true) return;
		if (authenticatedUser) {
			if (authenticatedUser.role === userRoles.SUPER_ADMIN) {
				router.push('/super-admin');
			} else if (
				[userRoles.CLIENT_ADMIN, userRoles.GLORIFIED_CLIENT_ADMIN].includes(
					authenticatedUser.role
				)
			) {
				if (!authenticatedUser.isOnboard) {
					router.push('/onboarding');
					toast.warning('Please complete onboarding');
				} else {
					router.push('/client-admin');
				}
			} else if (authenticatedUser.role > userRoles.CLIENT_ADMIN) {
				router.push('/user');
			}
		} else {
			router.push('/login');
		}
	}, [authenticatedUser, router, isLoggedOut]);

	return (
		<div className="w-full max-w-md">
			<Card>
				<CardContent>
					<Tabs defaultValue="qr" className="w-full mt-2">
						<TabsList className="grid w-full grid-cols-2">
							<TabsTrigger value="qr" className="flex items-center gap-2">
								<QrCode className="h-4 w-4" />
								QR Code
							</TabsTrigger>
							<TabsTrigger value="email" className="flex items-center gap-2">
								<Mail className="h-4 w-4" />
								Email
							</TabsTrigger>
						</TabsList>

						<TabsContent value="email" className="mt-6">
							<div className="w-full max-w-sm mx-auto">
								<LoginForm />
							</div>
						</TabsContent>

						<TabsContent value="qr" className="mt-6">
							<div className="flex flex-col items-center space-y-4">
								<div className="text-center space-y-2">
									<h1 className="text-2xl font-bold">Smart Login</h1>
									<p className="text-balance text-sm text-muted-foreground">
										Open your valluva app to scan the QR code below
									</p>
								</div>

								<div className="flex justify-center p-4 bg-white rounded-lg border-2 border-dashed border-gray-200">
									<QrLogin />
								</div>

								<div className="text-center">
									<p className="text-center text-sm">
										QR code updates automatically every 30 seconds
									</p>
								</div>
							</div>
						</TabsContent>
					</Tabs>
				</CardContent>
			</Card>
		</div>
	);
}
